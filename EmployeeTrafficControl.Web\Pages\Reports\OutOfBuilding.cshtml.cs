using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Core.Services;
using EmployeeTrafficControl.Services;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    public class OutOfBuildingModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly EmployeeStatusService _employeeStatusService;
        private readonly AuthenticationService _authService;

        public OutOfBuildingModel(ApplicationDbContext context, EmployeeStatusService employeeStatusService, AuthenticationService authService)
        {
            _context = context;
            _employeeStatusService = employeeStatusService;
            _authService = authService;
        }

        public List<EmployeeStatus> EmployeesOutOfBuilding { get; set; } = new();
        public int? BuildingId { get; set; }
        public List<Building> Buildings { get; set; } = new();

        // آمار
        public int TotalOutOfBuilding { get; set; }
        public int HourlyExitCount { get; set; }
        public int OfficialMissionCount { get; set; }
        public int FinalExitCount { get; set; }
        public int InVehicleCount { get; set; }

        public async Task<IActionResult> OnGetAsync(int? buildingId = null)
        {
            // بررسی احراز هویت
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session == null)
            {
                Response.Cookies.Delete("SessionToken");
                return RedirectToPage("/Account/Login");
            }

            BuildingId = buildingId;

            // بارگذاری لیست ساختمان‌ها برای فیلتر
            var accessibleBuildingIds = GetAccessibleBuildingIds(session.User);
            var buildingsQuery = _context.Buildings.AsQueryable();
            
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }
            
            Buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();

            // دریافت کارمندان خارج از ساختمان
            EmployeesOutOfBuilding = await _employeeStatusService.GetEmployeesOutOfBuildingAsync(BuildingId);

            // فیلتر بر اساس ساختمان انتخابی
            if (BuildingId.HasValue)
            {
                EmployeesOutOfBuilding = EmployeesOutOfBuilding
                    .Where(e => e.Employee.BuildingId == BuildingId.Value)
                    .ToList();
            }

            // محاسبه آمار
            CalculateStats();

            return Page();
        }

        private void CalculateStats()
        {
            TotalOutOfBuilding = EmployeesOutOfBuilding.Count;
            HourlyExitCount = EmployeesOutOfBuilding.Count(e => e.CurrentStatus == EmployeeCurrentStatus.HourlyExit);
            OfficialMissionCount = EmployeesOutOfBuilding.Count(e => e.CurrentStatus == EmployeeCurrentStatus.OfficialMission);
            FinalExitCount = EmployeesOutOfBuilding.Count(e => e.CurrentStatus == EmployeeCurrentStatus.OutOfOffice);
            InVehicleCount = EmployeesOutOfBuilding.Count(e => e.IsInVehicle);
        }

        private List<int>? GetAccessibleBuildingIds(User user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role == "Admin")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }
    }
}
