using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Core.Services;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    public class DailyEntryModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly AuthenticationService _authService;
        private readonly EmployeeStatusService _employeeStatusService;
        private readonly DailyAttendanceService _attendanceService;

        public DailyEntryModel(
            ApplicationDbContext context,
            AuthenticationService authService,
            EmployeeStatusService employeeStatusService,
            DailyAttendanceService attendanceService)
        {
            _context = context;
            _authService = authService;
            _employeeStatusService = employeeStatusService;
            _attendanceService = attendanceService;
        }

        public List<Employee> Employees { get; set; } = new();
        public List<DailyAttendance> TodayAttendances { get; set; } = new();
        public List<EmployeeStatus> EmployeeStatuses { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();
        public List<SelectListItem> Jobs { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? JobId { get; set; }

        public int TotalEmployees => Employees.Count;
        public int PresentCount => TodayAttendances.Count(a => a.CheckInTime.HasValue);
        public int AbsentCount => TotalEmployees - PresentCount;
        public int LateCount => TodayAttendances.Count(a => a.LateMinutes > 0);

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session == null)
            {
                Response.Cookies.Delete("SessionToken");
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            if (!CanRegisterTraffic(session.User.Role))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(session.User);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(User currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری لیست مشاغل
            var jobs = await _context.Jobs.OrderBy(j => j.Title).ToListAsync();
            Jobs = jobs.Select(j => new SelectListItem
            {
                Value = j.JobId.ToString(),
                Text = j.Title,
                Selected = j.JobId == JobId
            }).ToList();

            // بارگذاری کارمندان با فیلتر
            var employeesQuery = _context.Employees
                                        .Include(e => e.Building)
                                        .Include(e => e.Job)
                                        .Where(e => e.IsActive);

            // اعمال فیلتر ساختمان
            if (accessibleBuildingIds != null)
            {
                employeesQuery = employeesQuery.Where(e => accessibleBuildingIds.Contains(e.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                employeesQuery = employeesQuery.Where(e => e.BuildingId == BuildingId.Value);
            }

            if (JobId.HasValue)
            {
                employeesQuery = employeesQuery.Where(e => e.JobId == JobId.Value);
            }

            // اعمال فیلتر جستجو
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                employeesQuery = employeesQuery.Where(e =>
                    e.FirstName.ToLower().Contains(searchLower) ||
                    e.LastName.ToLower().Contains(searchLower) ||
                    e.PersonnelCode.ToLower().Contains(searchLower));
            }

            Employees = await employeesQuery.OrderBy(e => e.Building != null ? e.Building.Name : "")
                                           .ThenBy(e => e.FirstName)
                                           .ThenBy(e => e.LastName)
                                           .ToListAsync();

            // بارگذاری حضور امروز
            var today = DateTime.Today;
            var employeeIds = Employees.Select(e => e.EmployeeId).ToList();

            TodayAttendances = await _context.DailyAttendances
                                           .Where(da => da.Date.Date == today && employeeIds.Contains(da.EmployeeId))
                                           .ToListAsync();

            // بارگذاری وضعیت کارمندان
            EmployeeStatuses = await _context.EmployeeStatuses
                                           .Where(es => es.Date.Date == today && employeeIds.Contains(es.EmployeeId))
                                           .ToListAsync();
        }

        private List<int>? GetAccessibleBuildingIds(User user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role == "Admin")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanRegisterTraffic(string userRole)
        {
            return userRole == "Admin" || userRole == "Manager" || userRole == "Guard";
        }
    }
}
