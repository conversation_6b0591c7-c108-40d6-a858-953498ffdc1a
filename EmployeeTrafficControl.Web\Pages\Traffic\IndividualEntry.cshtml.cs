using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Core.Services;

namespace EmployeeTrafficControl.Web.Pages.Traffic
{
    public class IndividualEntryModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly AuthenticationService _authService;
        private readonly EmployeeStatusService _employeeStatusService;

        public IndividualEntryModel(
            ApplicationDbContext context,
            AuthenticationService authService,
            EmployeeStatusService employeeStatusService)
        {
            _context = context;
            _authService = authService;
            _employeeStatusService = employeeStatusService;
        }

        public List<EmployeeStatus> EmployeesCanEnter { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        public int OutOfOfficeCount { get; set; }
        public int HourlyExitCount { get; set; }
        public int MissionCount { get; set; }
        public int PresentCount { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session == null)
            {
                Response.Cookies.Delete("SessionToken");
                return RedirectToPage("/Account/Login");
            }

            // بررسی دسترسی
            if (!CanRegisterTraffic(session.User.Role))
            {
                TempData["ErrorMessage"] = "شما به این بخش دسترسی ندارید.";
                return RedirectToPage("/Dashboard");
            }

            try
            {
                await LoadDataAsync(session.User);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(User currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // دریافت کارمندان خارج از ساختمان
            EmployeesCanEnter = await _employeeStatusService.GetEmployeesOutOfBuildingAsync(BuildingId);

            // اضافه کردن کارمندانی که هنوز وضعیت امروز ندارند
            var today = DateTime.Today;
            var employeesWithoutStatus = await _context.Employees
                .Include(e => e.Building)
                .Include(e => e.Job)
                .Where(e => e.IsActive && 
                           !_context.EmployeeStatuses.Any(es => es.EmployeeId == e.EmployeeId && es.Date.Date == today))
                .ToListAsync();

            // فیلتر ساختمان برای کارمندان بدون وضعیت
            if (accessibleBuildingIds != null)
            {
                employeesWithoutStatus = employeesWithoutStatus.Where(e => accessibleBuildingIds.Contains(e.BuildingId)).ToList();
            }

            if (BuildingId.HasValue)
            {
                employeesWithoutStatus = employeesWithoutStatus.Where(e => e.BuildingId == BuildingId.Value).ToList();
            }

            // تبدیل کارمندان بدون وضعیت به EmployeeStatus
            var employeesWithoutStatusList = employeesWithoutStatus.Select(e => new EmployeeStatus
            {
                EmployeeId = e.EmployeeId,
                Employee = e,
                Date = today,
                CurrentStatus = EmployeeCurrentStatus.OutOfOffice,
                LastUpdated = DateTime.Now
            }).ToList();

            EmployeesCanEnter.AddRange(employeesWithoutStatusList);

            // اعمال فیلتر وضعیت
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                EmployeesCanEnter = StatusFilter switch
                {
                    "out" => EmployeesCanEnter.Where(es => es.CurrentStatus == EmployeeCurrentStatus.OutOfOffice).ToList(),
                    "hourly" => EmployeesCanEnter.Where(es => es.CurrentStatus == EmployeeCurrentStatus.HourlyExit).ToList(),
                    "mission" => EmployeesCanEnter.Where(es => es.CurrentStatus == EmployeeCurrentStatus.OfficialMission).ToList(),
                    _ => EmployeesCanEnter
                };
            }

            // اعمال فیلتر جستجو
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                EmployeesCanEnter = EmployeesCanEnter.Where(es =>
                    es.Employee.FirstName.ToLower().Contains(searchLower) ||
                    es.Employee.LastName.ToLower().Contains(searchLower) ||
                    es.Employee.PersonnelCode.ToLower().Contains(searchLower)).ToList();
            }

            // مرتب‌سازی
            EmployeesCanEnter = EmployeesCanEnter.OrderBy(es => es.Employee.Building?.Name ?? "")
                                               .ThenBy(es => es.Employee.FirstName)
                                               .ThenBy(es => es.Employee.LastName)
                                               .ToList();

            // محاسبه آمار
            await CalculateStatsAsync(accessibleBuildingIds);
        }

        private async Task CalculateStatsAsync(List<int>? accessibleBuildingIds)
        {
            var today = DateTime.Today;
            var query = _context.EmployeeStatuses
                               .Include(es => es.Employee)
                               .Where(es => es.Date.Date == today);

            if (accessibleBuildingIds != null)
            {
                query = query.Where(es => accessibleBuildingIds.Contains(es.Employee.BuildingId));
            }

            if (BuildingId.HasValue)
            {
                query = query.Where(es => es.Employee.BuildingId == BuildingId.Value);
            }

            var statuses = await query.ToListAsync();

            OutOfOfficeCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OutOfOffice);
            HourlyExitCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.HourlyExit);
            MissionCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.OfficialMission);
            PresentCount = statuses.Count(s => s.CurrentStatus == EmployeeCurrentStatus.PresentInBuilding);

            // اضافه کردن کارمندانی که هنوز وضعیت ندارند به OutOfOfficeCount
            var employeesWithoutStatusCount = await _context.Employees
                .Where(e => e.IsActive && 
                           !_context.EmployeeStatuses.Any(es => es.EmployeeId == e.EmployeeId && es.Date.Date == today))
                .CountAsync();

            if (accessibleBuildingIds != null)
            {
                employeesWithoutStatusCount = await _context.Employees
                    .Where(e => e.IsActive && 
                               accessibleBuildingIds.Contains(e.BuildingId) &&
                               !_context.EmployeeStatuses.Any(es => es.EmployeeId == e.EmployeeId && es.Date.Date == today))
                    .CountAsync();
            }

            if (BuildingId.HasValue)
            {
                employeesWithoutStatusCount = await _context.Employees
                    .Where(e => e.IsActive && 
                               e.BuildingId == BuildingId.Value &&
                               !_context.EmployeeStatuses.Any(es => es.EmployeeId == e.EmployeeId && es.Date.Date == today))
                    .CountAsync();
            }

            OutOfOfficeCount += employeesWithoutStatusCount;
        }

        private List<int>? GetAccessibleBuildingIds(User user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role == "Admin")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }

        private bool CanRegisterTraffic(string userRole)
        {
            return userRole == "Admin" || userRole == "Manager" || userRole == "Guard";
        }
    }
}
