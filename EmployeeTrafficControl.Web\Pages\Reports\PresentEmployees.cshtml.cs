using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using EmployeeTrafficControl.Data;
using EmployeeTrafficControl.Models;
using EmployeeTrafficControl.Services;
using EmployeeTrafficControl.Core.Services;

namespace EmployeeTrafficControl.Web.Pages.Reports
{
    public class PresentEmployeesModel : PageModel
    {
        private readonly ApplicationDbContext _context;
        private readonly AuthenticationService _authService;
        private readonly EmployeeStatusService _employeeStatusService;

        public PresentEmployeesModel(
            ApplicationDbContext context,
            AuthenticationService authService,
            EmployeeStatusService employeeStatusService)
        {
            _context = context;
            _authService = authService;
            _employeeStatusService = employeeStatusService;
        }

        public List<EmployeeStatus> PresentEmployees { get; set; } = new();
        public List<DailyAttendance> TodayAttendances { get; set; } = new();
        public List<SelectListItem> Buildings { get; set; } = new();
        public List<SelectListItem> Jobs { get; set; } = new();

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? BuildingId { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? JobId { get; set; }

        public int OnTimeCount { get; set; }
        public int LateCount { get; set; }
        public int OvertimeCount { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            // بررسی احراز هویت
            var sessionToken = Request.Cookies["SessionToken"];
            if (string.IsNullOrEmpty(sessionToken))
            {
                return RedirectToPage("/Account/Login");
            }

            var session = await _authService.ValidateSessionAsync(sessionToken);
            if (session == null)
            {
                Response.Cookies.Delete("SessionToken");
                return RedirectToPage("/Account/Login");
            }

            try
            {
                await LoadDataAsync(session.User);
                return Page();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "خطا در بارگذاری اطلاعات: " + ex.Message;
                return Page();
            }
        }

        private async Task LoadDataAsync(User currentUser)
        {
            // تعیین ساختمان‌های قابل دسترس
            var accessibleBuildingIds = GetAccessibleBuildingIds(currentUser);

            // بارگذاری لیست ساختمان‌ها
            var buildingsQuery = _context.Buildings.AsQueryable();
            if (accessibleBuildingIds != null)
            {
                buildingsQuery = buildingsQuery.Where(b => accessibleBuildingIds.Contains(b.BuildingId));
            }

            var buildings = await buildingsQuery.OrderBy(b => b.Name).ToListAsync();
            Buildings = buildings.Select(b => new SelectListItem
            {
                Value = b.BuildingId.ToString(),
                Text = b.Name,
                Selected = b.BuildingId == BuildingId
            }).ToList();

            // بارگذاری لیست مشاغل
            var jobs = await _context.Jobs.OrderBy(j => j.Title).ToListAsync();
            Jobs = jobs.Select(j => new SelectListItem
            {
                Value = j.JobId.ToString(),
                Text = j.Title,
                Selected = j.JobId == JobId
            }).ToList();

            // دریافت کارمندان حاضر در ساختمان
            PresentEmployees = await _employeeStatusService.GetPresentEmployeesAsync(BuildingId);

            // اعمال فیلتر شغل
            if (JobId.HasValue)
            {
                PresentEmployees = PresentEmployees.Where(es => es.Employee.JobId == JobId.Value).ToList();
            }

            // اعمال فیلتر جستجو
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                PresentEmployees = PresentEmployees.Where(es =>
                    es.Employee.FirstName.ToLower().Contains(searchLower) ||
                    es.Employee.LastName.ToLower().Contains(searchLower) ||
                    es.Employee.PersonnelCode.ToLower().Contains(searchLower)).ToList();
            }

            // بارگذاری حضور امروز
            var today = DateTime.Today;
            var employeeIds = PresentEmployees.Select(es => es.EmployeeId).ToList();

            TodayAttendances = await _context.DailyAttendances
                                           .Where(da => da.Date.Date == today && employeeIds.Contains(da.EmployeeId))
                                           .ToListAsync();

            // محاسبه آمار
            CalculateStats();
        }

        private void CalculateStats()
        {
            OnTimeCount = TodayAttendances.Count(a => a.LateMinutes == 0);
            LateCount = TodayAttendances.Count(a => a.LateMinutes > 0);

            // محاسبه اضافه کار (کسانی که بیش از 8 ساعت کار کرده‌اند)
            OvertimeCount = TodayAttendances.Count(a => 
                a.TotalWorkHours.HasValue && a.TotalWorkHours.Value.TotalHours > 8);
        }

        private List<int>? GetAccessibleBuildingIds(User user)
        {
            // اگر کاربر Admin است، به همه ساختمان‌ها دسترسی دارد
            if (user.Role == "Admin")
                return null;

            // اگر کاربر به ساختمان خاصی محدود است
            if (user.BuildingId.HasValue)
                return new List<int> { user.BuildingId.Value };

            // در غیر این صورت به همه دسترسی دارد
            return null;
        }
    }
}
